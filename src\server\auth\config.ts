import { baseUrl } from "@/utils/shard";
import { type DefaultSession, type NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      // ...other properties
      // role: UserRole;
    } & DefaultSession["user"];
  }

  // interface User {
  //   // ...other properties
  //   // role: UserRole;
  // }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    Credentials({
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>",
        },
        password: {
          label: "Password",
          type: "password",
          placeholder: "Enter your password",
        },
      },
      async authorize(credentials) {
     

        // Add your own logic here to validate credentials
        // This is a basic example - in production, you should:
        // 1. Hash and compare passwords securely
        // 2. Query your database for user verification
        // 3. Implement proper error handling

        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Example validation - replace with your own logic
        if (credentials.email === "<EMAIL>" && credentials.password === "password") {
          return {
            id: "1",
            name: "Admin User",
            email: "<EMAIL>",
          };
        }

        // Return null if user data could not be retrieved
        return null;
      },
    }),
    /**
     * ...add more providers here.
     *
     * You can add other providers like GitHub, Google, etc. For example:
     *
     * @see https://next-auth.js.org/providers/github
     */
  ],
  callbacks: {
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.sub,
      },
    }),
  },
} satisfies NextAuthConfig;
